#include "cc_numarray_tool.h"
#include "cc_assert.h"
#include <iostream>
#include <algorithm>  // 添加std::max和std::min支持
#include "opencv2/opencv.hpp"
#include "CalmCarLog.h"
namespace tongxing
{

    std::shared_ptr<NumArray> creat_numarray(const std::vector<int> &shape, NumArray::DataType type)
    {
        std::shared_ptr<NumArray> output(new NumArray);
        std::shared_ptr<BlobData> bolb(new BlobData);

        size_t data_size = GET_NUMARRAY_TYPE_WORLD_SIZE(type);
        for (int i : shape)
        {
            //  std::cout<<i<<std::endl;
            data_size *= i;
        }

        if (data_size > 0)
        {
            // std::cout << " data_size: "<<data_size << std::endl;
            cc_assert(bolb->init(data_size) == 0);
        }
        output->shape = shape;
        output->word_size = GET_NUMARRAY_TYPE_WORLD_SIZE(type);
        output->data = bolb->pu8VirAddr;
        output->data_blob_ptr = bolb;
        output->type = type;
        return output;
    }
    void ScaleNormPlane(const uint8_t *src,
                        int src_stride,
                        int src_width,
                        int src_height,
                        float *dst,
                        int dst_stride,
                        int dst_width,
                        int dst_height,
                        int step_input,
                        int step_output,
                        float std,
                        float mean)
    {

        float step_x = (float)src_width / (float)dst_width;
        float step_y = (float)src_height / (float)dst_height;
        size_t loop_num=dst_width * dst_height;
        for (int i = loop_num-1; i>=0;  i--)
        {
            int x = i % dst_width;
            int y = i / dst_width;
            float *output_ptr = dst + x * step_output + y * dst_stride;
            int input_x = x * step_x;
            int input_y = y * step_y;
            uint8_t *input_ptr = (uint8_t *)src + input_x * step_input + input_y * src_stride;
            // std::cout<<dst<<" "<<output_ptr<<std::endl;
            output_ptr[0] = (input_ptr[0] - mean) / std;
            // std::cout<<i<<" "<<dst_width<<" "<<dst_height<<std::endl;
            // std::cout<<mean<<" "<<std<<" "<<(int)input_ptr[0]<<" "<<output_ptr[0]<<std::endl;
        }
    }
    /**
     * @brief 图像缩放函数，使用双线性插值算法
     * @param src 源图像数据指针
     * @param src_stride 源图像行步长
     * @param src_width 源图像宽度
     * @param src_height 源图像高度
     * @param dst 目标图像数据指针
     * @param dst_stride 目标图像行步长
     * @param dst_width 目标图像宽度
     * @param dst_height 目标图像高度
     * @param step_input 输入步长
     * @param step_output 输出步长
     * @param flag_calc_histogram 是否计算直方图
     * @param histogram_data 直方图数据指针
     */
    void ScalePlane(const uint8_t *src,
                    int src_stride,
                    int src_width,
                    int src_height,
                    uint8_t *dst,
                    int dst_stride,
                    int dst_width,
                    int dst_height,
                    int step_input,
                    int step_output,
                    bool flag_calc_histogram,
                    uint64_t *histogram_data) noexcept
    {
        // 关键安全检查 - 仅检查最重要的安全问题
        if (src == nullptr || dst == nullptr) {
            return; // 静默返回，避免日志开销
        }

        // 修复除零错误 - 处理目标尺寸为1的特殊情况
        float step_x = 0.0f; // 初始化变量
        float step_y = 0.0f; // 初始化变量

        if (dst_width == 1) {
            step_x = 0.0f;
        } else {
            step_x = static_cast<float>(src_width - 1) / static_cast<float>(dst_width - 1);
        }

        if (dst_height == 1) {
            step_y = 0.0f;
        } else {
            step_y = static_cast<float>(src_height - 1) / static_cast<float>(dst_height - 1);
        }

        const size_t loop_num = static_cast<size_t>(dst_width) * static_cast<size_t>(dst_height);

        // 初始化直方图
        if (flag_calc_histogram && histogram_data != nullptr) {
            for (size_t j = 0; j < 256; ++j) {
                histogram_data[j] = 0;
            }
        }

        // 预计算边界值以减少循环内检查
        const int src_width_1 = src_width - 1;
        const int src_height_1 = src_height - 1;
        const bool is_single_width = (dst_width == 1);
        const bool is_single_height = (dst_height == 1);
        const float center_x = static_cast<float>(src_width_1) * 0.5f;
        const float center_y = static_cast<float>(src_height_1) * 0.5f;

        // 优化的循环 - 减少条件判断
        for (size_t i = 0; i < loop_num; ++i) {
            const int x = static_cast<int>(i % static_cast<size_t>(dst_width));
            const int y = static_cast<int>(i / static_cast<size_t>(dst_width));
            uint8_t *output_ptr = dst + x * step_output + y * dst_stride;

            // 快速坐标计算
            const float src_x = is_single_width ? center_x : static_cast<float>(x) * step_x;
            const float src_y = is_single_height ? center_y : static_cast<float>(y) * step_y;

            int x1 = static_cast<int>(src_x);
            int y1 = static_cast<int>(src_y);
            int x2 = x1 + 1;
            int y2 = y1 + 1;

            // 快速边界检查 - 使用位运算优化
            x1 = (x1 < 0) ? 0 : ((x1 > src_width_1) ? src_width_1 : x1);
            y1 = (y1 < 0) ? 0 : ((y1 > src_height_1) ? src_height_1 : y1);
            x2 = (x2 > src_width_1) ? src_width_1 : x2;
            y2 = (y2 > src_height_1) ? src_height_1 : y2;

            float dx = src_x - x1;
            float dy = src_y - y1;

            // 直接计算偏移 - 减少中间变量
            uint8_t *p11 = (uint8_t *)src + x1 * step_input + y1 * src_stride;
            uint8_t *p12 = (uint8_t *)src + x1 * step_input + y2 * src_stride;
            uint8_t *p21 = (uint8_t *)src + x2 * step_input + y1 * src_stride;
            uint8_t *p22 = (uint8_t *)src + x2 * step_input + y2 * src_stride;

            // 优化的双线性插值 - 减少乘法运算
            float w11 = (1.0f - dx) * (1.0f - dy);
            float w12 = (1.0f - dx) * dy;
            float w21 = dx * (1.0f - dy);
            float w22 = dx * dy;

            float val = w11 * p11[0] + w12 * p12[0] + w21 * p21[0] + w22 * p22[0];

            // 快速类型转换 - 移除范围检查（uint8_t自然截断）
            output_ptr[0] = static_cast<uint8_t>(val + 0.5f);

            // 直方图统计
            if (flag_calc_histogram && histogram_data != nullptr) {
                histogram_data[output_ptr[0]]++;
            }
        }
    }

    static void SetMap(const int input_base,
                       int src_stride,
                       int src_width,
                       int src_height,
                       int *dst,
                       int dst_stride,
                       int dst_width,
                       int dst_height,
                       int step_input,
                       int step_output,
                       float norm_std_value,
                       float norm_mean_value,
                       float *norm_std_dst,
                       float *norm_mean_dst)
    {
        // std::cout<<(void*)src<<" "<<(void*)dst<<std::endl;
        float step_x = (float)src_width / (float)dst_width;
        float step_y = (float)src_height / (float)dst_height;
        for (int i = 0; i < dst_width * dst_height; i++)
        {
            int x = i % dst_width;
            int y = i / dst_width;
            int output_index = x * step_output + y * dst_stride;
            int *output_ptr = dst + output_index;
            int input_x = x * step_x;
            int input_y = y * step_y;
            int input_index = input_base + input_x * step_input + input_y * src_stride;
            output_ptr[0] = input_index;
            if (norm_std_dst)
            {
                norm_std_dst[output_index] = norm_std_value;
            }
            if (norm_mean_dst)
            {
                norm_mean_dst[output_index] = norm_mean_value;
            }
        }
    }
    static void SetNormParam(const float value,
                             float *dst,
                             int dst_stride,
                             int dst_width,
                             int dst_height,
                             int step_output)
    {
        for (int i = 0; i < dst_width * dst_height; i++)
        {
            int x = i % dst_width;
            int y = i / dst_width;
            float *output_ptr = dst + x * step_output + y * dst_stride;
            output_ptr[0] = value;
        }
    }
    void creat_corp_resize_map(std::shared_ptr<std::vector<int>> &index_map, std::shared_ptr<std::vector<float>> &std_map, std::shared_ptr<std::vector<float>> &mean_map, int width, int height, int ch, const NumArrayResizeNormParam &param, const NumArrayCorpParam &corp_param, bool flag_input_nchw, bool flag_output_nchw)
    {
        index_map.reset(new std::vector<int>);
        int output_height = param.output_h;
        int output_width = param.output_w;
        int output_stride = flag_output_nchw ? param.output_w : param.output_w * ch;
        int step_input = flag_input_nchw ? 1 : ch;
        int step_output = flag_output_nchw ? 1 : ch;
        int input_width_ = width;
        int input_height_ = height;
        int input_stride = flag_input_nchw ? width : width * ch;
        int input_height = input_height_;
        int input_width = input_width_;
        int x_offset = 0;
        int y_offset = 0;
            int input_left_pad = 0;
            int input_top_pad = 0;
            int input_right_pad = 0;
            int input_buttom_pad = 0;
            int output_left_pad = 0;
            int output_top_pad = 0;
            int output_right_pad = 0;
            int output_buttom_pad = 0;

        float scale_h = 1;
        float scale_w = 1;
        int resize_width = param.output_w * scale_w;
        int resize_height = param.output_h * scale_h;
        index_map->resize(output_height * output_width * ch);
        int *map_data_ptr = index_map->data();
        memset(map_data_ptr, 0, index_map->size() * sizeof(int));
        if (param.flag_norm)
        {
            std_map.reset(new std::vector<float>);
            mean_map.reset(new std::vector<float>);
            std_map->resize(output_height * output_width * ch);
            mean_map->resize(output_height * output_width * ch);
            memset(std_map->data(), 0, std_map->size() * sizeof(float));
            memset(mean_map->data(), 0, mean_map->size() * sizeof(float));
        }


            if (corp_param.flag_corp)
            {
                // std::cout<<"corp_param: "<<corp_param.x<<" "<<corp_param.y<<" "<<corp_param.w<<" "<<corp_param.h<<std::endl;
                x_offset = corp_param.x >= 0 ? corp_param.x : 0;
                y_offset = corp_param.y >= 0 ? corp_param.y : 0;
                input_left_pad = corp_param.x < 0 ? -corp_param.x : 0;
                input_top_pad = corp_param.y < 0 ? -corp_param.y : 0;
                input_height = (corp_param.h + y_offset) <= input_height_ ? corp_param.h : input_height_ - y_offset;
                input_width = (corp_param.w + x_offset) <= input_width_ ? corp_param.w : input_width_ - x_offset;

                 input_buttom_pad = (corp_param.h + y_offset) <= input_height_ ? 0 : (corp_param.h + y_offset) - input_height_;
                 input_right_pad = (corp_param.w + x_offset) <= input_width_ ? 0 : (corp_param.w + x_offset) - input_width_;
                // if (param.keep_ratio)
                // {
                    // input_height -= (input_buttom_pad);
                    // input_width -= (input_right_pad);
                // }
                input_height-=(input_top_pad+input_buttom_pad);
                input_width-=(input_left_pad+input_right_pad);
                // std::cout<<"corp_param2: "<<x_offset<<" "<<y_offset<<" "<<input_width<<" "<<input_height<<" "<<input_left_pad<<" "<<input_top_pad<<std::endl;
                
            }
 

            if (param.keep_ratio)
            {
                float scale_r_h = (float)(input_height + input_top_pad + input_buttom_pad) / (float)(output_height);
                float scale_r_w = (float)(input_width + input_left_pad + input_right_pad) / (float)(output_width);
                if (scale_r_h > scale_r_w)
                {
                    scale_h = (float)output_height / (float)(input_height + input_top_pad + input_buttom_pad);
                    resize_width = (float)(input_width + input_left_pad + input_right_pad) * scale_h;
                    output_left_pad = input_left_pad * scale_h;
                    output_top_pad = input_top_pad * scale_h;
                    output_right_pad = input_right_pad * scale_h;
                    output_buttom_pad = input_buttom_pad * scale_h;
                }
                else
                {
                    scale_w = (float)output_width / (float)(input_width + input_left_pad + input_right_pad);
                    // std::cout<<"ssssssssss:"<<(input_width + input_left_pad + input_right_pad)<<std::endl;
                    resize_height = (float)(input_height + input_top_pad + input_buttom_pad) * scale_w;
                    output_left_pad = input_left_pad * scale_w;
                    output_top_pad = input_top_pad * scale_w;
                    output_right_pad = input_right_pad * scale_w;
                    output_buttom_pad = input_buttom_pad * scale_w;
                }
                resize_width -= (output_left_pad + output_right_pad);
                resize_height -= (output_top_pad + output_buttom_pad);
                // std::cout<<scale_w<<" "<<scale_h<<std::endl;
                // std::cout<<(float)(input_width)<<" "<<(float)(input_height)<<std::endl;
                // std::cout<<(float)(input_width + input_left_pad + input_right_pad)<<" "<<(float)(input_height + input_top_pad + input_buttom_pad)<<std::endl;
                // std::cout<<resize_width<<" "<<resize_height<<std::endl;
                // std::cout << output_top_pad << " " << output_left_pad << " " << input_left_pad << std::endl;
            }
            else
            {
                // float scale_r_h =  (float)(output_height)/(float)(input_height + input_top_pad + input_buttom_pad);
                // float scale_r_w =  (float)(output_width)/(float)(input_width + input_left_pad + input_right_pad);
                output_left_pad = 0;
                output_top_pad = 0;
                output_right_pad = 0;
                output_buttom_pad = 0;
            }
        for (int j = 0; j < ch; j++)
        {
            int input_base;
            int *output_ptr;
            float *output_mean_ptr = param.flag_norm ? mean_map->data() : NULL;
            float *output_std_ptr = param.flag_norm ? std_map->data() : NULL;
            if (flag_input_nchw)
            {
                input_base = j * input_stride * input_height_ + y_offset * input_stride + x_offset;
            }
            else
            {
                input_base = j + y_offset * input_stride + x_offset * ch;
            }

            if (flag_output_nchw)
            {
                output_ptr = map_data_ptr + j * output_stride * output_height+output_left_pad+output_stride*output_top_pad;
                if (output_std_ptr && output_mean_ptr)
                {
                    output_std_ptr = output_std_ptr + j * output_stride * output_height+output_left_pad+output_stride*output_top_pad;
                    output_mean_ptr = output_mean_ptr + j * output_stride * output_height+output_left_pad+output_stride*output_top_pad;
                }
            }
            else
            {
                output_ptr = map_data_ptr + j+ output_top_pad * output_stride + output_left_pad * ch;
                if (output_std_ptr && output_mean_ptr)
                {
                    output_std_ptr = output_std_ptr + j+ output_top_pad * output_stride + output_left_pad * ch;
                    output_mean_ptr = output_mean_ptr + j+ output_top_pad * output_stride + output_left_pad * ch;
                }
            }
            // std::cout<<"input_width:"<<input_width<<std::endl;
            SetMap(input_base, input_stride,
                   input_width, input_height,
                   output_ptr, output_stride,
                   resize_width, resize_height, step_input, step_output,
                   param.norm_std[j] == 0 ? 0 : 1.0 / param.norm_std[j],
                   param.norm_mean[j],
                   output_std_ptr,
                   output_mean_ptr);
        }
    }
    std::shared_ptr<NumArray> numarray_remap_normalization(const std::shared_ptr<NumArray> &input, const std::vector<int> &ouput_dim, std::shared_ptr<std::vector<int>> &index, std::shared_ptr<std::vector<float>> &norm_param_std, std::shared_ptr<std::vector<float>> &norm_param_mean)
    {

        // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        uint8_t *input_data_ptr = input->data;
        int loop_num = index->size();
        int *index_data = index->data()+loop_num-1;
        if (norm_param_std && norm_param_mean)
        {
            std::shared_ptr<NumArray> output = creat_numarray(ouput_dim, NumArray::DataType::FLOAT32);
            float *output_data_ptr = (float *)output->data+loop_num-1;
            float *norm_param_std_data = norm_param_std->data()+loop_num-1;
            float *norm_param_mean_data = norm_param_mean->data()+loop_num-1;
            for (int i = loop_num-1; i>=0; i--)
            {
                // std::cout<<norm_param_mean_data[i]<<" "<<norm_param_std_data[i]<<std::endl;

                (*output_data_ptr--) = (*(input_data_ptr + *(index_data--)) - (*norm_param_mean_data--)) * (*norm_param_std_data--);
            }
            TX_LOG_DEBUG("cc_numarray_tool.cpp","debug");
            // {
            //     cv::Mat m(cv::Size(ouput_dim[3], ouput_dim[2]), CV_32FC1, output->data);
            //     cv::imwrite("test_resize.jpg", m*255);
            //     // exit(0);
            // }
            return output;
        }
        else
        {

            std::shared_ptr<NumArray> output = creat_numarray(ouput_dim, NumArray::DataType::UINT8);
            uint8_t *output_data_ptr = (uint8_t *)output->data+loop_num-1;
            for (int i = loop_num-1; i >=0 ; i--)
            {
                (*output_data_ptr--) = *(input_data_ptr + *(index_data--));
            }

            // {
            //     cv::Mat m(cv::Size(ouput_dim[3], ouput_dim[2] * 3), CV_8UC1, output->data);
            //     cv::imwrite("test_resize.jpg", m);
            //     exit(0);
            // }
            return output;
        }
    }
    std::shared_ptr<NumArray> numarray_corp_resize_pad_normalization(const std::shared_ptr<NumArray> &input, const NumArrayResizeNormParam &param,
     const std::vector<NumArrayCorpParam> &corp_param, bool flag_calc_histogram,
      std::shared_ptr<NumArray> &pixels_histogram, bool flag_input_nchw, bool flag_output_nchw)
    {
        cc_assert(input);
        cc_assert(input->shape.size() == 4);
        int batch = input->shape[0];
        int ch = input->shape[1];

        int input_stride = input->shape[3];
        int output_stride = param.output_w;

        int output_height = param.output_h;
        int output_width = param.output_w;
        int input_width_ = input->shape[3];
        int input_height_ = input->shape[2];
        if (flag_input_nchw == false)
        {
            ch = input->shape[3];
            input_width_ = input->shape[2];
            input_height_ = input->shape[1];
            input_stride = input->shape[2] * ch;
        }
        if (flag_output_nchw == false)
        {
            output_stride = param.output_w * ch;
        }
        // std::cout<<ch<<" "<<input_height_<<" "<<input_width_<<" "<<input_stride<<std::endl;
        // std::cout<<input_stride<<" "<<output_stride<<std::endl;
        // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        //  NumArray::DataType output_type=param.flag_norm?NumArray::DataType::FLOAT32:NumArray::DataType::UINT8;
        std::shared_ptr<NumArray> output;
        // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        std::shared_ptr<NumArray> output_norm;
        if (param.flag_norm)
        {
            // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
            if (flag_output_nchw)
            {
                output_norm = creat_numarray({batch, ch, param.output_h, param.output_w}, NumArray::DataType::FLOAT32);
            }
            else
            {
                output_norm = creat_numarray({batch, param.output_h, param.output_w, ch}, NumArray::DataType::FLOAT32);
            }
             memset(output_norm->data_blob_ptr->pu8VirAddr, 0, output_norm->data_blob_ptr->u32Size);
            // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        }
        else
        {
            if (flag_output_nchw)
            {
                output = creat_numarray({batch, ch, param.output_h, param.output_w}, NumArray::DataType::UINT8);
            }
            else
            {
                output = creat_numarray({batch, param.output_h, param.output_w, ch}, NumArray::DataType::UINT8);
            }
             memset(output->data_blob_ptr->pu8VirAddr, 0, output->data_blob_ptr->u32Size);
        }
        // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        int step_input = flag_input_nchw ? 1 : ch;
        int step_output = flag_output_nchw ? 1 : ch;
        // std::cout<<step_input<<" "<<step_output<<std::endl;

        for (int i = 0; i < batch; i++)
        {
            // auto output_tensor = output->getTensor<unsigned char>()->operator[](i);
            auto input_tensor = input->getTensor<unsigned char>()->operator[](i);

            int input_height = input_height_;
            int input_width = input_width_;
            int x_offset = 0;
            int y_offset = 0;
            int input_left_pad = 0;
            int input_top_pad = 0;
            int input_right_pad = 0;
            int input_buttom_pad = 0;
            int output_left_pad = 0;
            int output_top_pad = 0;
            int output_right_pad = 0;
            int output_buttom_pad = 0;
            if (corp_param[i].flag_corp)
            {
                // std::cout<<"corp_param["<<i<<"]: "<<corp_param[i].x<<" "<<corp_param[i].y<<" "<<corp_param[i].w<<" "<<corp_param[i].h<<std::endl;
                x_offset = corp_param[i].x >= 0 ? corp_param[i].x : 0;
                y_offset = corp_param[i].y >= 0 ? corp_param[i].y : 0;
                input_left_pad = corp_param[i].x < 0 ? -corp_param[i].x : 0;
                input_top_pad = corp_param[i].y < 0 ? -corp_param[i].y : 0;
                input_height = (corp_param[i].h + y_offset) <= input_height_ ? corp_param[i].h : input_height_ - y_offset;
                input_width = (corp_param[i].w + x_offset) <= input_width_ ? corp_param[i].w : input_width_ - x_offset;

                input_buttom_pad = (input_height + y_offset) <= input_height_ ? 0 : (input_height + y_offset) - input_height_;
                input_right_pad = (input_width + x_offset) <= input_width_ ? 0 : (input_width + x_offset) - input_width_;
                // if (param.keep_ratio)
                // {
                    // input_height -= (input_buttom_pad);
                    // input_width -= (input_right_pad);
                // }
                input_height-=(input_top_pad+input_buttom_pad);
                input_width-=(input_left_pad+input_right_pad);
                // std::cout<<"corp_param2["<<i<<"]: "<<x_offset<<" "<<y_offset<<" "<<input_width<<" "<<input_height<<" "<<input_left_pad<<" "<<input_top_pad<<std::endl;
            }
            float scale_h = 1;
            float scale_w = 1;
            int resize_width = param.output_w * scale_w;
            int resize_height = param.output_h * scale_h;
            if (param.keep_ratio)
            {
                float scale_r_h = (float)(input_height + input_top_pad + input_buttom_pad) / (float)(output_height);
                float scale_r_w = (float)(input_width + input_left_pad + input_right_pad) / (float)(output_width);
                if (scale_r_h > scale_r_w)
                {
                    scale_h = (float)output_height / (float)(input_height + input_top_pad + input_buttom_pad);
                    resize_width = (float)(input_width + input_left_pad + input_right_pad) * scale_h;
                    output_left_pad = input_left_pad * scale_h;
                    output_top_pad = input_top_pad * scale_h;
                    output_right_pad = input_right_pad * scale_h;
                    output_buttom_pad = input_buttom_pad * scale_h;
                }
                else
                {
                    scale_w = (float)output_width / (float)(input_width + input_left_pad + input_right_pad);
                    // std::cout<<"ssssssssss:"<<(input_width + input_left_pad + input_right_pad)<<std::endl;
                    resize_height = (float)(input_height + input_top_pad + input_buttom_pad) * scale_w;
                    output_left_pad = input_left_pad * scale_w;
                    output_top_pad = input_top_pad * scale_w;
                    output_right_pad = input_right_pad * scale_w;
                    output_buttom_pad = input_buttom_pad * scale_w;
                }
                resize_width -= (output_left_pad + output_right_pad);
                resize_height -= (output_top_pad + output_buttom_pad);
                // std::cout<<scale_w<<" "<<scale_h<<std::endl;
                // std::cout<<(float)(input_width)<<" "<<(float)(input_height)<<std::endl;
                // std::cout<<(float)(input_width + input_left_pad + input_right_pad)<<" "<<(float)(input_height + input_top_pad + input_buttom_pad)<<std::endl;
                // std::cout<<resize_width<<" "<<resize_height<<std::endl;
                // std::cout << output_top_pad << " " << output_left_pad << " " << input_left_pad << std::endl;
            }
            else
            {
                // float scale_r_h =  (float)(output_height)/(float)(input_height + input_top_pad + input_buttom_pad);
                // float scale_r_w =  (float)(output_width)/(float)(input_width + input_left_pad + input_right_pad);
                output_left_pad = 0;
                output_top_pad = 0;
                output_right_pad = 0;
                output_buttom_pad = 0;
            }

            // std::cout<<resize_width<<" "<<resize_height<<" "<<scale_w<<" "<<scale_h<<std::endl;
            // std::cout<<ch<<std::endl;
            for (int j = 0; j < ch; j++)
            {
                // std::cout<<"j:"<<j<<std::endl;
                // auto input_tensor_ch = input_tensor;
                // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
                if (!param.flag_norm)
                {
                    auto output_tensor = output->getTensor<unsigned char>()->operator[](i);
                    uint8_t *input_ptr;
                    uint8_t *output_ptr;
                    if (flag_input_nchw)
                    {
                        input_ptr = input_tensor.data_ + j * input_stride * input_height_ + y_offset * input_stride + x_offset;
                    }
                    else
                    {
                        input_ptr = input_tensor.data_ + j + y_offset * input_stride + x_offset * ch;
                    }

                    if (flag_output_nchw)
                    {
                        output_ptr = output_tensor.data_ + j * output_stride * output_height + output_top_pad * output_stride + output_left_pad;
                    }
                    else
                    {
                        output_ptr = output_tensor.data_ + j + output_top_pad * output_stride + output_left_pad * ch;
                    }
                    uint64_t *hist_ptr = (uint64_t *)pixels_histogram->data;
                    ScalePlane(input_ptr, input_stride,
                               input_width, input_height,
                               output_ptr, output_stride,
                               resize_width, resize_height, step_input, step_output, flag_calc_histogram, hist_ptr);
                    // static int cnt = 0;
                    // cv::Mat mat_output(96, 96, CV_8UC1, output_ptr);
                    // cv::imwrite("tmp_eye_"+std::to_string(cnt++)+".jpg", mat_output);
                    // std::cout << "save " << cnt << "image... : (void *)pixels_histogram->data:" << (void *)pixels_histogram->data << std::endl;
                    // cnt = 0;
                }
                else
                {
                    // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
                    auto output_norm_tensor_buf = output_norm->getTensor<float>()->operator[](i).data_;
                    // TX_LOG_DEBUG("numarray_corp_resize_pad_normalization","ScaleNormPlane start");
                    // std::cout << resize_width << "," << resize_height << std::endl;
                    uint8_t *input_ptr;
                    float *output_ptr;
                    if (flag_input_nchw)
                    {
                        input_ptr = input_tensor.data_ + j * input_stride * input_height_ + y_offset * input_stride + x_offset;
                    }
                    else
                    {
                        input_ptr = input_tensor.data_ + j + y_offset * input_stride + x_offset * ch;
                    }
                    if (flag_output_nchw)
                    {
                        output_ptr = output_norm_tensor_buf + j * output_stride * output_height + output_top_pad * output_stride + output_left_pad;
                    }
                    else
                    {
                        output_ptr = output_norm_tensor_buf + j + output_top_pad * output_stride + output_left_pad * ch;
                    }
                    ScaleNormPlane(input_ptr, input_stride,
                                   input_width, input_height,
                                   output_ptr, output_stride,
                                   resize_width, resize_height, step_input, step_output, param.norm_std[j], param.norm_mean[j]);
                    // TX_LOG_DEBUG("numarray_corp_resize_pad_normalization","ScaleNormPlane end");
                    // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
                }
            }
        }
        // std::cout << __FILE__ << ":" << __LINE__ << std::endl;
        if (!param.flag_norm)
        {
            // if (!flag_output_nchw)
            // {
            //     cv::Mat m(cv::Size(output_width, output_height), CV_8UC3, output->data);
            //     cv::imwrite("test_resize.jpg", m);
            // }
            // else
            // {
            //     cv::Mat m(cv::Size(output_width, output_height), CV_8UC1, output->data);
            //     cv::imwrite("test_resize.jpg", m);
            // }
            return output;
        }
        else
        {
            // if (!flag_output_nchw)
            // {
            //     cv::Mat m(cv::Size(output_width, output_height), CV_32FC3, output_norm->data);
            //     cv::imwrite("test_resize"+std::to_string(output_width)+".jpg", m*255);
            // }
            // else
            // {
            //     cv::Mat m(cv::Size(output_width, output_height), CV_32FC1, output_norm->data);
            //     cv::imwrite("test_resize"+std::to_string(output_width)+".jpg", m*255);
            // }
            return output_norm;
        }
    }
    std::shared_ptr<NumArray> numarray_reshape(const std::shared_ptr<NumArray> &input, const std::vector<int> &reshape)
    {
        std::vector<int> input_shape = input->shape;
        int input_data_size = 1;
        int output_data_size = 1;
        for (int i = 0; i < input_shape.size(); i++)
        {
            input_data_size *= input_shape[i];
        }
        for (int i = 0; i < reshape.size(); i++)
        {
            output_data_size *= reshape[i];
        }

        cc_assert(input_data_size == output_data_size);
        std::shared_ptr<NumArray> output(new NumArray);
        output->type = input->type;
        output->data = input->data;
        output->word_size = input->word_size;
        output->data_blob_ptr = input->data_blob_ptr;
        output->shape = reshape;
        return output;
    }
    std::shared_ptr<NumArray> numarray_extend(const std::shared_ptr<NumArray> &input, int dim, int extend_num, bool flag_copy)
    {
        // 参数验证
        if (!input) {
            std::cerr << "numarray_extend: 输入数组为空" << std::endl;
            return nullptr;
        }

        if (input->shape.empty()) {
            std::cerr << "numarray_extend: 输入数组shape为空" << std::endl;
            return nullptr;
        }

        if (dim < 0 || dim >= static_cast<int>(input->shape.size())) {
            std::cerr << "numarray_extend: 维度索引 " << dim << " 超出范围 [0, " << input->shape.size() << ")" << std::endl;
            return nullptr;
        }

        if (extend_num < 0) {
            std::cerr << "numarray_extend: 扩展数量不能为负数: " << extend_num << std::endl;
            return nullptr;
        }

        if (extend_num == 0) {
            // 如果不需要扩展，直接返回原数组的副本
            return numarray_reshape(input, input->shape);
        }

        std::vector<int> input_shape = input->shape;
        std::vector<int> output_shape = input_shape;  // 直接复制而不是循环

        // 检查扩展后的维度是否会溢出
        if (output_shape[dim] > INT_MAX - extend_num) {
            std::cerr << "numarray_extend: 扩展后维度大小溢出" << std::endl;
            return nullptr;
        }
        output_shape[dim] += extend_num;

        int input_ch_num = 1;
        int output_ch_num = 1;
        int data_size = 1;
        int world_size = input->word_size;

        // 计算各部分大小，同时检查溢出
        for (int i = 0; i < dim; i++)
        {
            if (input_ch_num > INT_MAX / input_shape[i] || output_ch_num > INT_MAX / output_shape[i]) {
                std::cerr << "numarray_extend: 计算过程中发生整数溢出" << std::endl;
                return nullptr;
            }
            input_ch_num *= input_shape[i];
            output_ch_num *= output_shape[i];
        }

        for (size_t i = dim + 1; i < input_shape.size(); i++)
        {
            if (data_size > INT_MAX / input_shape[i]) {
                std::cerr << "numarray_extend: 数据大小计算溢出" << std::endl;
                return nullptr;
            }
            data_size *= input_shape[i];
        }

        auto input_reshape = numarray_reshape(input, {input_ch_num, input_shape[dim], data_size});
        if (!input_reshape) {
            std::cerr << "numarray_extend: 输入数组重塑失败" << std::endl;
            return nullptr;
        }

        auto output = creat_numarray({output_ch_num, output_shape[dim], data_size}, input->type);
        if (!output) {
            std::cerr << "numarray_extend: 输出数组创建失败" << std::endl;
            return nullptr;
        }

        // 优化内存拷贝操作
        size_t copy_size = data_size * world_size;
        for (int i = 0; i < output_ch_num; i++)
        {
            uint8_t *input_reshape_ptr = input_reshape->data + i * data_size * input_shape[dim] * world_size;
            uint8_t *output_ptr = output->data + i * data_size * output_shape[dim] * world_size;

            // 批量拷贝原始数据
            size_t original_data_size = input_shape[dim] * copy_size;
            memcpy(output_ptr, input_reshape_ptr, original_data_size);

            // 如果需要复制数据来填充扩展部分
            if (flag_copy && extend_num > 0)
            {
                uint8_t *extend_start = output_ptr + original_data_size;
                // 重复拷贝第一个元素来填充扩展部分
                for (int j = 0; j < extend_num; j++)
                {
                    memcpy(extend_start + j * copy_size, input_reshape_ptr, copy_size);
                }
            }
        }

        return numarray_reshape(output, output_shape);
    }
    std::shared_ptr<NumArray> numarray_nhwc2nchw(const std::shared_ptr<NumArray> &input)
    {
        std::vector<int> new_dim;

        new_dim.push_back(input->shape[0]);
        new_dim.push_back(input->shape[3]);
        new_dim.push_back(input->shape[1]);
        new_dim.push_back(input->shape[2]);
        std::shared_ptr<NumArray> new_numarray = creat_numarray(new_dim, input->type);
        unsigned char *src_data = input->data;
        unsigned char *dst_data = new_numarray->data;

        int dst_n_size = new_dim[1] * new_dim[2] * new_dim[3] * new_numarray->word_size;
        int dst_c_size = new_dim[2] * new_dim[3] * new_numarray->word_size;
        int dst_h_size = new_dim[3] * new_numarray->word_size;
        int dst_w_size = new_numarray->word_size;

        int data_len = new_dim[0] * new_dim[1] * new_dim[2] * new_dim[3] * new_numarray->word_size;

        int src_n_size = input->shape[1] * input->shape[2] * input->shape[3] * input->word_size;
        int src_h_size = input->shape[2] * input->shape[3] * input->word_size;
        int src_w_size = input->shape[3] * input->word_size;
        int src_c_size = input->word_size;
        int word_size = input->word_size;

        int dst_n = 0;
        int dst_c = 0;
        int dst_h = 0;
        int dst_w = 0;

        int src_n = 0;
        int src_c = 0;
        int src_h = 0;
        int src_w = 0;

        int word_index;
        // std::cout<<data_len<<std::endl;
        for (int i = 0; i < data_len; i++)
        {
            word_index = i % word_size;
            dst_n = i / dst_n_size;
            dst_c = (i % dst_n_size) / dst_c_size;
            dst_h = (i % dst_c_size) / dst_h_size;
            dst_w = (i % dst_h_size) / dst_w_size;

            // std::cout<<src_n<<" "<<src_c<<" "<<src_h<<" "<<src_w<<" "<<word_index<<std::endl;
            // std::cout<<i<<" "<<src_n * src_n_size + src_c * src_c_size + src_h * src_h_size + src_w * src_w_size + word_index<<std::endl;;
            dst_data[i] = src_data[dst_n * src_n_size + dst_c * src_c_size + dst_h * src_h_size + dst_w * src_w_size + word_index];
        }
        // cv::Mat m(cv::Size(new_dim[3],new_dim[2]*3),CV_8UC1,dst_data);
        // std::cout<<cv::Size(new_dim[3],new_dim[2]*3)<<std::endl;
        // cv::imwrite("nchw.jpg",m);
        return new_numarray;
    }

    std::shared_ptr<NumArray> numarray_nchw2nhwc(std::shared_ptr<NumArray> input) 
    {
        std::vector<int> input_shape = input->shape; // NCHW 格式
        int N = input_shape[0];
        int C = input_shape[1];
        int H = input_shape[2];
        int W = input_shape[3];

        // 目标形状：NHWC
        std::vector<int> output_shape = {N, H, W, C};
        std::shared_ptr<NumArray> output = creat_numarray(output_shape, input->type);

        // 根据 word_size 计算数据拷贝单位
        int word_size = input->word_size;
        uint8_t *input_data = (uint8_t *)input->data;
        uint8_t *output_data = (uint8_t *)output->data;

        // 遍历 N, C, H, W 重新排列数据
        for (int n = 0; n < N; n++) {
            for (int h = 0; h < H; h++) {
                for (int w = 0; w < W; w++) {
                    for (int c = 0; c < C; c++) {
                        memcpy(
                            output_data + (n * H * W * C + h * W * C + w * C + c) * word_size,
                            input_data + (n * C * H * W + c * H * W + h * W + w) * word_size,
                            word_size);
                    }
                }
            }
        }
        return output;
    }

}