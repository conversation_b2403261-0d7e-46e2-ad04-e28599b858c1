/**
 * @file test_scale_functions_simple.cpp
 * @brief 简化版单元测试：验证ScalePlane和ScaleNormPlane函数修复
 * <AUTHOR> Assistant
 * @date 2025-07-11
 * 
 * 这是一个独立的测试文件，不依赖复杂的项目结构
 */

#include <iostream>
#include <vector>
#include <cstring>
#include <cmath>
#include <algorithm>

// 简化的测试框架
struct TestResult {
    int total = 0;
    int passed = 0;
    int failed = 0;
    
    void add(bool success) {
        total++;
        if (success) {
            passed++;
            std::cout << " ✅";
        } else {
            failed++;
            std::cout << " ❌";
        }
    }
    
    void summary() {
        std::cout << "\n" << std::string(50, '=') << std::endl;
        std::cout << "测试总结: " << passed << "/" << total << " 通过";
        if (failed > 0) {
            std::cout << " (" << failed << " 失败)";
        }
        std::cout << std::endl;
        std::cout << std::string(50, '=') << std::endl;
    }
};

TestResult g_result;

// 复制修复后的ScalePlane函数（简化版，移除依赖）
void ScalePlane_Fixed(const uint8_t *src,
                     int src_stride,
                     int src_width,
                     int src_height,
                     uint8_t *dst,
                     int dst_stride,
                     int dst_width,
                     int dst_height,
                     int step_input,
                     int step_output,
                     bool flag_calc_histogram = false,
                     uint64_t *histogram_data = nullptr)
{
    // 参数验证 - 防止空指针和无效参数
    if (src == nullptr || dst == nullptr) {
        std::cerr << "ScalePlane: 输入或输出指针为空" << std::endl;
        return;
    }
    
    if (src_width <= 0 || src_height <= 0 || dst_width <= 0 || dst_height <= 0) {
        std::cerr << "ScalePlane: 图像尺寸必须为正数" << std::endl;
        return;
    }
    
    if (src_stride < src_width || dst_stride < dst_width) {
        std::cerr << "ScalePlane: stride参数不合理" << std::endl;
        return;
    }
    
    // 修复除零错误 - 处理目标尺寸为1的特殊情况
    float step_x, step_y;
    if (dst_width == 1) {
        step_x = 0.0f;  // 当目标宽度为1时，始终采样源图像中心
    } else {
        step_x = (float)(src_width - 1) / (float)(dst_width - 1);
    }
    
    if (dst_height == 1) {
        step_y = 0.0f;  // 当目标高度为1时，始终采样源图像中心
    } else {
        step_y = (float)(src_height - 1) / (float)(dst_height - 1);
    }
    
    size_t loop_num = dst_width * dst_height;
    
    // 初始化直方图
    if (flag_calc_histogram && histogram_data != nullptr) {
        for (size_t j = 0; j < 256; j++)
            histogram_data[j] = 0;
    }

    // 使用正序循环以提高缓存局部性
    for (size_t i = 0; i < loop_num; i++) {
        int x = i % dst_width;
        int y = i / dst_width;
        uint8_t *output_ptr = dst + x * step_output + y * dst_stride;
        
        // 计算源图像坐标
        float src_x = (dst_width == 1) ? (src_width - 1) * 0.5f : x * step_x;
        float src_y = (dst_height == 1) ? (src_height - 1) * 0.5f : y * step_y;
        
        int x1 = static_cast<int>(src_x);
        int y1 = static_cast<int>(src_y);
        int x2 = x1 + 1;
        int y2 = y1 + 1;
        
        // 改进的边界检查
        x1 = std::max(0, std::min(x1, src_width - 1));
        y1 = std::max(0, std::min(y1, src_height - 1));
        x2 = std::max(0, std::min(x2, src_width - 1));
        y2 = std::max(0, std::min(y2, src_height - 1));
        
        float dx = src_x - x1;
        float dy = src_y - y1;
        
        // 边界检查 - 确保内存访问安全
        size_t offset_11 = x1 * step_input + y1 * src_stride;
        size_t offset_12 = x1 * step_input + y2 * src_stride;
        size_t offset_21 = x2 * step_input + y1 * src_stride;
        size_t offset_22 = x2 * step_input + y2 * src_stride;
        
        // 获取四个邻近像素
        uint8_t *p11 = (uint8_t *)src + offset_11;
        uint8_t *p12 = (uint8_t *)src + offset_12;
        uint8_t *p21 = (uint8_t *)src + offset_21;
        uint8_t *p22 = (uint8_t *)src + offset_22;
        
        // 双线性插值
        float val = (1.0f - dx) * (1.0f - dy) * p11[0] +
                    (1.0f - dx) * dy * p12[0] +
                    dx * (1.0f - dy) * p21[0] +
                    dx * dy * p22[0];
        
        // 安全的类型转换 - 确保值在有效范围内
        val = std::max(0.0f, std::min(255.0f, val + 0.5f));
        output_ptr[0] = static_cast<uint8_t>(val);
        
        // 修复的直方图统计 - 移除无效检查
        if (flag_calc_histogram && histogram_data != nullptr) {
            histogram_data[output_ptr[0]]++;
        }
    }
}

// 复制修复后的ScaleNormPlane函数（简化版）
void ScaleNormPlane_Fixed(const uint8_t *src,
                         int src_stride,
                         int src_width,
                         int src_height,
                         float *dst,
                         int dst_stride,
                         int dst_width,
                         int dst_height,
                         int step_input,
                         int step_output,
                         float std_val,
                         float mean)
{
    // 参数验证 - 防止空指针和无效参数
    if (src == nullptr || dst == nullptr) {
        std::cerr << "ScaleNormPlane: 输入或输出指针为空" << std::endl;
        return;
    }
    
    if (src_width <= 0 || src_height <= 0 || dst_width <= 0 || dst_height <= 0) {
        std::cerr << "ScaleNormPlane: 图像尺寸必须为正数" << std::endl;
        return;
    }
    
    if (std_val == 0.0f) {
        std::cerr << "ScaleNormPlane: 标准差不能为零" << std::endl;
        return;
    }

    float step_x = (float)src_width / (float)dst_width;
    float step_y = (float)src_height / (float)dst_height;
    size_t loop_num = dst_width * dst_height;
    
    // 使用正序循环以提高缓存局部性
    for (size_t i = 0; i < loop_num; i++)
    {
        int x = i % dst_width;
        int y = i / dst_width;
        float *output_ptr = dst + x * step_output + y * dst_stride;
        
        // 改进的坐标计算 - 使用浮点精度
        float src_x = (x + 0.5f) * step_x - 0.5f;
        float src_y = (y + 0.5f) * step_y - 0.5f;
        
        // 边界检查
        int input_x = std::max(0, std::min(static_cast<int>(src_x + 0.5f), src_width - 1));
        int input_y = std::max(0, std::min(static_cast<int>(src_y + 0.5f), src_height - 1));
        
        // 安全的内存访问
        size_t offset = input_x * step_input + input_y * src_stride;
        uint8_t *input_ptr = (uint8_t *)src + offset;
        
        // 归一化计算
        output_ptr[0] = (static_cast<float>(input_ptr[0]) - mean) / std_val;
    }
}

// 创建测试图像
std::vector<uint8_t> create_test_image(int width, int height, uint8_t value = 128) {
    std::vector<uint8_t> data(width * height);
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            data[y * width + x] = (x + y + value) % 256;
        }
    }
    return data;
}

// 测试函数
void test_scale_plane_fixes() {
    std::cout << "🧪 测试ScalePlane修复..." << std::endl;
    
    // 测试1: 空指针安全性
    std::cout << "测试1: 空指针检查";
    std::vector<uint8_t> dst(100);
    ScalePlane_Fixed(nullptr, 10, 10, 10, dst.data(), 10, 10, 10, 1, 1);
    ScalePlane_Fixed(create_test_image(10, 10).data(), 10, 10, 10, nullptr, 10, 10, 10, 1, 1);
    g_result.add(true); // 如果没有崩溃就算通过
    
    // 测试2: 除零错误修复
    std::cout << "\n测试2: 除零错误修复";
    auto src = create_test_image(10, 10);
    std::vector<uint8_t> dst1(1);
    ScalePlane_Fixed(src.data(), 10, 10, 10, dst1.data(), 1, 1, 1, 1, 1);
    g_result.add(true); // 如果没有崩溃就算通过
    
    // 测试3: 正常缩放
    std::cout << "\n测试3: 正常缩放功能";
    auto src2 = create_test_image(4, 4);
    std::vector<uint8_t> dst2(16);
    ScalePlane_Fixed(src2.data(), 4, 4, 4, dst2.data(), 4, 4, 4, 1, 1);
    bool has_data = false;
    for (auto val : dst2) {
        if (val != 0) { has_data = true; break; }
    }
    g_result.add(has_data);
    
    // 测试4: 直方图功能
    std::cout << "\n测试4: 直方图功能";
    std::vector<uint64_t> histogram(256, 0);
    ScalePlane_Fixed(src2.data(), 4, 4, 4, dst2.data(), 4, 4, 4, 1, 1, true, histogram.data());
    uint64_t total = 0;
    for (auto count : histogram) total += count;
    g_result.add(total == 16); // 应该统计16个像素
}

void test_scale_norm_plane_fixes() {
    std::cout << "\n\n🧪 测试ScaleNormPlane修复..." << std::endl;
    
    // 测试1: 空指针安全性
    std::cout << "测试1: 空指针检查";
    std::vector<float> dst(100);
    ScaleNormPlane_Fixed(nullptr, 10, 10, 10, dst.data(), 10, 10, 10, 1, 1, 1.0f, 0.0f);
    ScaleNormPlane_Fixed(create_test_image(10, 10).data(), 10, 10, 10, nullptr, 10, 10, 10, 1, 1, 1.0f, 0.0f);
    g_result.add(true);
    
    // 测试2: 标准差为零检查
    std::cout << "\n测试2: 标准差为零检查";
    auto src = create_test_image(4, 4);
    std::vector<float> dst1(16);
    ScaleNormPlane_Fixed(src.data(), 4, 4, 4, dst1.data(), 4, 4, 4, 1, 1, 0.0f, 128.0f);
    g_result.add(true); // 应该安全返回
    
    // 测试3: 正常归一化
    std::cout << "\n测试3: 正常归一化功能";
    auto src2 = create_test_image(2, 2, 128);
    std::vector<float> dst2(4);
    ScaleNormPlane_Fixed(src2.data(), 2, 2, 2, dst2.data(), 2, 2, 2, 1, 1, 64.0f, 128.0f);
    bool correct_norm = true;
    for (float val : dst2) {
        if (std::abs(val) > 2.0f) { // 允许一定误差
            correct_norm = false;
            break;
        }
    }
    g_result.add(correct_norm);
}

int main() {
    std::cout << "🚀 开始ScalePlane和ScaleNormPlane修复验证测试\n" << std::endl;
    
    test_scale_plane_fixes();
    test_scale_norm_plane_fixes();
    
    g_result.summary();
    
    return g_result.failed > 0 ? 1 : 0;
}
