# 单独编译cc_numarray_tool修复测试的CMake配置
# 使用方法：
# mkdir build_test && cd build_test
# cmake -f ../test/CMakeLists_test_fixes.txt ..
# make test_cc_numarray_tool_fixes

cmake_minimum_required(VERSION 3.10)
project(cc_numarray_tool_test)

set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 添加包含目录
include_directories(
    .
    tongxing_util/src/util
    tongxing_util/src/memory_manager
)

# 编译tongxing_util相关源文件
set(TONGXING_UTIL_SOURCES
    tongxing_util/src/util/cc_numarray_tool.cpp
    tongxing_util/src/util/cc_tensor.cpp
    tongxing_util/src/memory_manager/cc_blob_data.cpp
    tongxing_util/src/memory_manager/cc_tiny_mempool.cpp
)

# 查找必要的源文件（如果存在）
foreach(src_file ${TONGXING_UTIL_SOURCES})
    if(EXISTS "${CMAKE_SOURCE_DIR}/${src_file}")
        list(APPEND EXISTING_SOURCES ${src_file})
    else()
        message(WARNING "源文件不存在: ${src_file}")
    endif()
endforeach()

# 创建测试可执行文件
add_executable(test_cc_numarray_tool_fixes
    test/test_cc_numarray_tool_fixes.cpp
    ${EXISTING_SOURCES}
)

# 链接数学库
target_link_libraries(test_cc_numarray_tool_fixes m)

# 设置编译选项
target_compile_options(test_cc_numarray_tool_fixes PRIVATE
    -Wall
    -Wextra
    -g
    -O0  # 调试模式，不优化
)

# 添加预处理器定义
target_compile_definitions(test_cc_numarray_tool_fixes PRIVATE
    -DTEST_MODE=1
)

message(STATUS "测试可执行文件配置完成")
message(STATUS "编译命令: make test_cc_numarray_tool_fixes")
message(STATUS "运行命令: ./test_cc_numarray_tool_fixes")
