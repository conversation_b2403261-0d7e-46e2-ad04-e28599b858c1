/**
 * @file test_cc_numarray_tool_fixes.cpp
 * @brief 单元测试：验证cc_numarray_tool.cpp中修复的函数
 * <AUTHOR> Assistant
 * @date 2025-07-11
 */

#include <iostream>
#include <vector>
#include <memory>
#include <cstring>
#include <cassert>
#include <cmath>
#include "../tongxing_util/src/util/cc_numarray_tool.h"
#include "../tongxing_util/src/util/cc_tensor.h"

using namespace tongxing;

// 测试结果统计
struct TestStats {
    int total_tests = 0;
    int passed_tests = 0;
    int failed_tests = 0;
    
    void add_result(bool passed) {
        total_tests++;
        if (passed) {
            passed_tests++;
            std::cout << "✅ PASS" << std::endl;
        } else {
            failed_tests++;
            std::cout << "❌ FAIL" << std::endl;
        }
    }
    
    void print_summary() {
        std::cout << "\n" << "="*50 << std::endl;
        std::cout << "测试总结:" << std::endl;
        std::cout << "总测试数: " << total_tests << std::endl;
        std::cout << "通过: " << passed_tests << std::endl;
        std::cout << "失败: " << failed_tests << std::endl;
        std::cout << "成功率: " << (total_tests > 0 ? (passed_tests * 100.0 / total_tests) : 0) << "%" << std::endl;
        std::cout << "="*50 << std::endl;
    }
};

TestStats g_stats;

// 测试辅助宏
#define TEST_CASE(name) \
    std::cout << "\n🧪 测试: " << name << " ... "; \
    bool test_result = true;

#define EXPECT_TRUE(condition) \
    if (!(condition)) { \
        std::cout << "\n   期望为真但实际为假: " << #condition; \
        test_result = false; \
    }

#define EXPECT_FALSE(condition) \
    if (condition) { \
        std::cout << "\n   期望为假但实际为真: " << #condition; \
        test_result = false; \
    }

#define EXPECT_EQ(expected, actual) \
    if ((expected) != (actual)) { \
        std::cout << "\n   期望: " << (expected) << ", 实际: " << (actual); \
        test_result = false; \
    }

#define EXPECT_NULL(ptr) \
    if ((ptr) != nullptr) { \
        std::cout << "\n   期望为空指针但实际不为空"; \
        test_result = false; \
    }

#define EXPECT_NOT_NULL(ptr) \
    if ((ptr) == nullptr) { \
        std::cout << "\n   期望非空指针但实际为空"; \
        test_result = false; \
    }

#define TEST_END() \
    g_stats.add_result(test_result);

// 创建测试用的简单图像数据
std::vector<uint8_t> create_test_image(int width, int height, uint8_t value = 128) {
    std::vector<uint8_t> data(width * height, value);
    // 创建简单的渐变图案
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            data[y * width + x] = (x + y) % 256;
        }
    }
    return data;
}

// 测试ScalePlane函数
void test_ScalePlane() {
    std::cout << "\n" << "="*30 << " ScalePlane测试 " << "="*30 << std::endl;
    
    // 测试1: 空指针检查
    TEST_CASE("ScalePlane - 空指针检查")
    {
        std::vector<uint8_t> dst(100);
        // 这些调用应该安全返回而不崩溃
        ScalePlane(nullptr, 10, 10, 10, dst.data(), 10, 10, 10, 1, 1);
        ScalePlane(create_test_image(10, 10).data(), 10, 10, 10, nullptr, 10, 10, 10, 1, 1);
        EXPECT_TRUE(true); // 如果程序没有崩溃，测试通过
    }
    TEST_END()
    
    // 测试2: 无效尺寸检查
    TEST_CASE("ScalePlane - 无效尺寸检查")
    {
        auto src = create_test_image(10, 10);
        std::vector<uint8_t> dst(100);
        // 这些调用应该安全返回
        ScalePlane(src.data(), 10, 0, 10, dst.data(), 10, 10, 10, 1, 1);
        ScalePlane(src.data(), 10, 10, -1, dst.data(), 10, 10, 10, 1, 1);
        ScalePlane(src.data(), 10, 10, 10, dst.data(), 10, 0, 10, 1, 1);
        EXPECT_TRUE(true); // 如果程序没有崩溃，测试通过
    }
    TEST_END()
    
    // 测试3: 除零错误修复 - 目标尺寸为1
    TEST_CASE("ScalePlane - 目标尺寸为1的除零修复")
    {
        auto src = create_test_image(10, 10);
        std::vector<uint8_t> dst(1);
        // 这个调用在修复前会导致除零错误
        ScalePlane(src.data(), 10, 10, 10, dst.data(), 1, 1, 1, 1, 1);
        EXPECT_TRUE(true); // 如果程序没有崩溃，测试通过
    }
    TEST_END()
    
    // 测试4: 正常缩放功能
    TEST_CASE("ScalePlane - 正常缩放功能")
    {
        auto src = create_test_image(4, 4);
        std::vector<uint8_t> dst(16); // 2x2 -> 4x4
        ScalePlane(src.data(), 4, 4, 4, dst.data(), 4, 4, 4, 1, 1);
        
        // 验证输出不全为零（说明有数据被处理）
        bool has_non_zero = false;
        for (uint8_t val : dst) {
            if (val != 0) {
                has_non_zero = true;
                break;
            }
        }
        EXPECT_TRUE(has_non_zero);
    }
    TEST_END()
    
    // 测试5: 直方图功能
    TEST_CASE("ScalePlane - 直方图功能")
    {
        auto src = create_test_image(4, 4);
        std::vector<uint8_t> dst(16);
        std::vector<uint64_t> histogram(256, 0);
        
        ScalePlane(src.data(), 4, 4, 4, dst.data(), 4, 4, 4, 1, 1, true, histogram.data());
        
        // 验证直方图有数据
        uint64_t total_pixels = 0;
        for (uint64_t count : histogram) {
            total_pixels += count;
        }
        EXPECT_EQ(16, total_pixels); // 应该统计了所有像素
    }
    TEST_END()
}

// 测试ScaleNormPlane函数
void test_ScaleNormPlane() {
    std::cout << "\n" << "="*30 << " ScaleNormPlane测试 " << "="*30 << std::endl;

    // 测试1: 空指针检查
    TEST_CASE("ScaleNormPlane - 空指针检查")
    {
        std::vector<float> dst(100);
        ScaleNormPlane(nullptr, 10, 10, 10, dst.data(), 10, 10, 10, 1, 1, 1.0f, 0.0f);
        ScaleNormPlane(create_test_image(10, 10).data(), 10, 10, 10, nullptr, 10, 10, 10, 1, 1, 1.0f, 0.0f);
        EXPECT_TRUE(true);
    }
    TEST_END()

    // 测试2: 标准差为零检查
    TEST_CASE("ScaleNormPlane - 标准差为零检查")
    {
        auto src = create_test_image(4, 4);
        std::vector<float> dst(16);
        ScaleNormPlane(src.data(), 4, 4, 4, dst.data(), 4, 4, 4, 1, 1, 0.0f, 128.0f);
        EXPECT_TRUE(true); // 应该安全返回而不崩溃
    }
    TEST_END()

    // 测试3: 正常归一化功能
    TEST_CASE("ScaleNormPlane - 正常归一化功能")
    {
        auto src = create_test_image(2, 2, 128); // 创建值为128的图像
        std::vector<float> dst(4);
        ScaleNormPlane(src.data(), 2, 2, 2, dst.data(), 2, 2, 2, 1, 1, 64.0f, 128.0f);

        // 验证归一化结果：(128 - 128) / 64 = 0
        for (float val : dst) {
            EXPECT_TRUE(std::abs(val - 0.0f) < 0.001f);
        }
    }
    TEST_END()
}

// 测试creat_numarray函数
void test_creat_numarray() {
    std::cout << "\n" << "="*30 << " creat_numarray测试 " << "="*30 << std::endl;

    // 测试1: 空shape检查
    TEST_CASE("creat_numarray - 空shape检查")
    {
        std::vector<int> empty_shape;
        auto result = creat_numarray(empty_shape, NumArray::DataType::UINT8);
        EXPECT_NULL(result);
    }
    TEST_END()

    // 测试2: 负数维度检查
    TEST_CASE("creat_numarray - 负数维度检查")
    {
        std::vector<int> invalid_shape = {10, -5, 20};
        auto result = creat_numarray(invalid_shape, NumArray::DataType::UINT8);
        EXPECT_NULL(result);
    }
    TEST_END()

    // 测试3: 零维度检查
    TEST_CASE("creat_numarray - 零维度检查")
    {
        std::vector<int> zero_shape = {10, 0, 20};
        auto result = creat_numarray(zero_shape, NumArray::DataType::UINT8);
        EXPECT_NULL(result);
    }
    TEST_END()

    // 测试4: 正常创建
    TEST_CASE("creat_numarray - 正常创建")
    {
        std::vector<int> valid_shape = {2, 3, 4};
        auto result = creat_numarray(valid_shape, NumArray::DataType::UINT8);
        EXPECT_NOT_NULL(result);
        if (result) {
            EXPECT_EQ(valid_shape.size(), result->shape.size());
            EXPECT_EQ(2, result->shape[0]);
            EXPECT_EQ(3, result->shape[1]);
            EXPECT_EQ(4, result->shape[2]);
            EXPECT_NOT_NULL(result->data);
        }
    }
    TEST_END()

    // 测试5: 大尺寸溢出检查（模拟）
    TEST_CASE("creat_numarray - 大尺寸处理")
    {
        std::vector<int> large_shape = {1000, 1000, 1000}; // 可能导致溢出
        auto result = creat_numarray(large_shape, NumArray::DataType::UINT8);
        // 根据系统内存情况，这可能成功或失败，但不应该崩溃
        EXPECT_TRUE(true); // 主要测试不崩溃
    }
    TEST_END()
}

// 测试numarray_extend函数
void test_numarray_extend() {
    std::cout << "\n" << "="*30 << " numarray_extend测试 " << "="*30 << std::endl;

    // 测试1: 空输入检查
    TEST_CASE("numarray_extend - 空输入检查")
    {
        auto result = numarray_extend(nullptr, 0, 5, true);
        EXPECT_NULL(result);
    }
    TEST_END()

    // 测试2: 无效维度索引检查
    TEST_CASE("numarray_extend - 无效维度索引检查")
    {
        auto input = creat_numarray({2, 3, 4}, NumArray::DataType::UINT8);
        if (input) {
            auto result = numarray_extend(input, -1, 5, true); // 负索引
            EXPECT_NULL(result);

            result = numarray_extend(input, 5, 5, true); // 超出范围
            EXPECT_NULL(result);
        }
    }
    TEST_END()

    // 测试3: 负扩展数量检查
    TEST_CASE("numarray_extend - 负扩展数量检查")
    {
        auto input = creat_numarray({2, 3, 4}, NumArray::DataType::UINT8);
        if (input) {
            auto result = numarray_extend(input, 1, -5, true);
            EXPECT_NULL(result);
        }
    }
    TEST_END()

    // 测试4: 零扩展处理
    TEST_CASE("numarray_extend - 零扩展处理")
    {
        auto input = creat_numarray({2, 3, 4}, NumArray::DataType::UINT8);
        if (input) {
            auto result = numarray_extend(input, 1, 0, true);
            EXPECT_NOT_NULL(result);
            if (result) {
                EXPECT_EQ(input->shape.size(), result->shape.size());
                EXPECT_EQ(input->shape[1], result->shape[1]); // 维度应该不变
            }
        }
    }
    TEST_END()

    // 测试5: 正常扩展
    TEST_CASE("numarray_extend - 正常扩展")
    {
        auto input = creat_numarray({2, 3, 4}, NumArray::DataType::UINT8);
        if (input) {
            auto result = numarray_extend(input, 1, 2, true); // 在维度1上扩展2
            EXPECT_NOT_NULL(result);
            if (result) {
                EXPECT_EQ(3, result->shape.size());
                EXPECT_EQ(2, result->shape[0]);
                EXPECT_EQ(5, result->shape[1]); // 3 + 2 = 5
                EXPECT_EQ(4, result->shape[2]);
            }
        }
    }
    TEST_END()
}

int main() {
    std::cout << "🚀 开始cc_numarray_tool修复验证测试" << std::endl;

    // 运行所有测试
    test_ScalePlane();
    test_ScaleNormPlane();
    test_creat_numarray();
    test_numarray_extend();

    // 打印测试总结
    g_stats.print_summary();

    return g_stats.failed_tests == 0 ? 0 : 1;
}
